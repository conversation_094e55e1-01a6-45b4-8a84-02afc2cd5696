"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"

export function NavigationLoader() {
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()

  useEffect(() => {
    const originalPush = router.push

    router.push = (...args) => {
      setIsLoading(true)
      const result = originalPush.apply(router, args)

      setTimeout(() => {
        setIsLoading(false)
      }, 1000)

      return result
    }

    return () => {
      router.push = originalPush
    }
  }, [router])

  if (!isLoading) return null

  return (
    <div className="fixed top-0 left-0 right-0 z-50">
      <div className="h-1 bg-primary animate-[loading-bar_1s_ease-in-out_infinite] origin-left"></div>
    </div>
  )
}
