"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Star, ThumbsUp, MessageCircle, User } from "lucide-react"

interface Review {
  id: number
  userName: string
  rating: number
  comment: string
  date: string
  helpful: number
  verified: boolean
}

interface RecipeRatingProps {
  recipeId: number
  recipeName: string
  averageRating: number
  totalReviews: number
  reviews: Review[]
}

export function RecipeRating({ recipeId, recipeName, averageRating, totalReviews, reviews }: RecipeRatingProps) {
  const [userRating, setUserRating] = useState(0)
  const [hoverRating, setHoverRating] = useState(0)
  const [reviewText, setReviewText] = useState("")
  const [userName, setUserName] = useState("")
  const [showReviewForm, setShowReviewForm] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleStarClick = (rating: number) => {
    setUserRating(rating)
  }

  const handleSubmitReview = async () => {
    if (!userRating || !reviewText.trim() || !userName.trim()) return

    setIsSubmitting(true)
    // Simulate API call
    setTimeout(() => {
      setIsSubmitting(false)
      setShowReviewForm(false)
      setUserRating(0)
      setReviewText("")
      setUserName("")
      // In real app, would refresh reviews
    }, 1000)
  }

  const renderStars = (rating: number, interactive = false, size = "w-5 h-5") => {
    return (
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`${size} cursor-pointer transition-colors ${
              star <= (interactive ? hoverRating || userRating : rating)
                ? "fill-yellow-400 text-yellow-400"
                : "text-gray-300"
            }`}
            onClick={() => interactive && handleStarClick(star)}
            onMouseEnter={() => interactive && setHoverRating(star)}
            onMouseLeave={() => interactive && setHoverRating(0)}
          />
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Rating Summary */}
      <Card className="border-0 shadow-soft rounded-2xl">
        <CardHeader>
          <CardTitle className="text-xl font-semibold text-slate-gray">Recipe Reviews</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-6 mb-6">
            <div className="text-center">
              <div className="text-4xl font-bold text-slate-gray mb-2">{averageRating.toFixed(1)}</div>
              {renderStars(averageRating, false, "w-6 h-6")}
              <div className="text-sm text-slate-gray/70 mt-2">{totalReviews} reviews</div>
            </div>
            <div className="flex-1">
              {[5, 4, 3, 2, 1].map((stars) => {
                const count = reviews.filter((r) => Math.floor(r.rating) === stars).length
                const percentage = totalReviews > 0 ? (count / totalReviews) * 100 : 0
                return (
                  <div key={stars} className="flex items-center gap-2 mb-1">
                    <span className="text-sm text-slate-gray w-8">{stars}★</span>
                    <div className="flex-1 bg-warm-sand/30 rounded-full h-2">
                      <div
                        className="bg-yellow-400 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${percentage}%` }}
                      />
                    </div>
                    <span className="text-sm text-slate-gray/70 w-8">{count}</span>
                  </div>
                )
              })}
            </div>
          </div>

          <Button
            onClick={() => setShowReviewForm(!showReviewForm)}
            className="bg-muted-teal hover:bg-muted-teal/90 text-white rounded-xl"
          >
            <Star className="w-4 h-4 mr-2" />
            Write a Review
          </Button>
        </CardContent>
      </Card>

      {/* Review Form */}
      {showReviewForm && (
        <Card className="border-0 shadow-soft rounded-2xl">
          <CardHeader>
            <CardTitle className="text-lg font-semibold text-slate-gray">Share Your Experience</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-slate-gray mb-2">Your Rating</label>
              {renderStars(userRating, true, "w-8 h-8")}
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-gray mb-2">Your Name</label>
              <Input
                value={userName}
                onChange={(e) => setUserName(e.target.value)}
                placeholder="Enter your name"
                className="rounded-xl border-warm-sand focus:ring-2 focus:ring-muted-teal focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-gray mb-2">Your Review</label>
              <Textarea
                value={reviewText}
                onChange={(e) => setReviewText(e.target.value)}
                placeholder="Share your thoughts about this recipe..."
                rows={4}
                className="rounded-xl border-warm-sand focus:ring-2 focus:ring-muted-teal focus:border-transparent resize-none"
              />
            </div>

            <div className="flex gap-3">
              <Button
                onClick={handleSubmitReview}
                disabled={!userRating || !reviewText.trim() || !userName.trim() || isSubmitting}
                className="bg-muted-teal hover:bg-muted-teal/90 text-white rounded-xl"
              >
                {isSubmitting ? "Submitting..." : "Submit Review"}
              </Button>
              <Button
                variant="outline"
                onClick={() => setShowReviewForm(false)}
                className="rounded-xl border-warm-sand"
              >
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Reviews List */}
      <div className="space-y-4">
        {reviews.map((review) => (
          <Card key={review.id} className="border-0 shadow-soft rounded-2xl">
            <CardContent className="pt-6">
              <div className="flex items-start gap-4">
                <div className="w-10 h-10 bg-muted-teal/10 rounded-full flex items-center justify-center">
                  <User className="w-5 h-5 text-muted-teal" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="font-medium text-slate-gray">{review.userName}</span>
                    {review.verified && (
                      <Badge variant="secondary" className="text-xs bg-green-100 text-green-700">
                        Verified Cook
                      </Badge>
                    )}
                    <span className="text-sm text-slate-gray/70">•</span>
                    <span className="text-sm text-slate-gray/70">{review.date}</span>
                  </div>

                  <div className="flex items-center gap-2 mb-3">{renderStars(review.rating, false, "w-4 h-4")}</div>

                  <p className="text-slate-gray mb-4">{review.comment}</p>

                  <div className="flex items-center gap-4">
                    <Button variant="ghost" size="sm" className="text-slate-gray/70 hover:text-muted-teal">
                      <ThumbsUp className="w-4 h-4 mr-1" />
                      Helpful ({review.helpful})
                    </Button>
                    <Button variant="ghost" size="sm" className="text-slate-gray/70 hover:text-muted-teal">
                      <MessageCircle className="w-4 h-4 mr-1" />
                      Reply
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
