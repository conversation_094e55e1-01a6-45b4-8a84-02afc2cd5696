@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  /* Updated to use the specified calm & modern color scheme */
  --primary-teal: #3aafa9;
  --secondary-sand: #e4cda7;
  --accent-coral: #f76c6c;
  --neutral-slate: #2e3a40;
  --background-off-white: #fafaf9;

  /* Updated Shadcn variables to use the custom color scheme */
  --background: #fafaf9;
  --foreground: #2e3a40;
  --card: #fafaf9;
  --card-foreground: #2e3a40;
  --popover: #fafaf9;
  --popover-foreground: #2e3a40;
  --primary: #3aafa9;
  --primary-foreground: #fafaf9;
  --secondary: #e4cda7;
  --secondary-foreground: #2e3a40;
  --muted: #f5f5f4;
  --muted-foreground: #6b7280;
  --accent: #f76c6c;
  --accent-foreground: #fafaf9;
  --destructive: #ef4444;
  --destructive-foreground: #fafaf9;
  --border: #e5e7eb;
  --input: #e5e7eb;
  --ring: #3aafa9;
  --chart-1: #3aafa9;
  --chart-2: #f76c6c;
  --chart-3: #e4cda7;
  --chart-4: #2e3a40;
  --chart-5: #6b7280;
  --radius: 0.625rem;
  --sidebar: #fafaf9;
  --sidebar-foreground: #2e3a40;
  --sidebar-primary: #3aafa9;
  --sidebar-primary-foreground: #fafaf9;
  --sidebar-accent: #e4cda7;
  --sidebar-accent-foreground: #2e3a40;
  --sidebar-border: #e5e7eb;
  --sidebar-ring: #3aafa9;
}

.dark {
  --background: #1a1a1a;
  --foreground: #fafaf9;
  --card: #262626;
  --card-foreground: #fafaf9;
  --popover: #262626;
  --popover-foreground: #fafaf9;
  --primary: #3aafa9;
  --primary-foreground: #1a1a1a;
  --secondary: #404040;
  --secondary-foreground: #fafaf9;
  --muted: #404040;
  --muted-foreground: #a3a3a3;
  --accent: #f76c6c;
  --accent-foreground: #1a1a1a;
  --destructive: #ef4444;
  --destructive-foreground: #fafaf9;
  --border: #404040;
  --input: #404040;
  --ring: #3aafa9;
  --chart-1: #3aafa9;
  --chart-2: #f76c6c;
  --chart-3: #e4cda7;
  --chart-4: #a3a3a3;
  --chart-5: #737373;
  --sidebar: #262626;
  --sidebar-foreground: #fafaf9;
  --sidebar-primary: #3aafa9;
  --sidebar-primary-foreground: #1a1a1a;
  --sidebar-accent: #404040;
  --sidebar-accent-foreground: #fafaf9;
  --sidebar-border: #404040;
  --sidebar-ring: #3aafa9;
}

@theme inline {
  --font-sans: var(--font-inter);
  --font-heading: var(--font-poppins);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: var(--font-heading);
  }
}

/* Updated shadow utilities to use the teal primary color */
.shadow-soft {
  box-shadow: 0 4px 20px -2px rgba(58, 175, 169, 0.1), 0 2px 8px -2px rgba(58, 175, 169, 0.06);
}

.shadow-soft-hover {
  box-shadow: 0 8px 30px -2px rgba(58, 175, 169, 0.15), 0 4px 12px -2px rgba(58, 175, 169, 0.1);
}

/* Added scroll animation styles */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(2rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.7s ease-out forwards;
}

/* Adding route transition loading animations */
@keyframes loading {
  0% {
    transform: scaleX(0);
  }
  50% {
    transform: scaleX(0.5);
  }
  100% {
    transform: scaleX(1);
  }
}

@keyframes loading-bar {
  0% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-loading-pulse {
  animation: pulse 1.5s ease-in-out infinite;
}
