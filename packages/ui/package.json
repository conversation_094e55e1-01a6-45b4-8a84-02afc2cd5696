{"name": "@timewithtuuli/ui", "version": "0.0.0", "private": true, "exports": {"./*": "./src/*.tsx"}, "scripts": {"lint": "eslint . --max-warnings 0", "generate:component": "turbo gen react-component", "check-types": "tsc --noEmit"}, "devDependencies": {"@timewithtuuli/eslint-config": "workspace:*", "@timewithtuuli/typescript-config": "workspace:*", "@types/node": "^22.15.3", "@types/react": "19.1.0", "@types/react-dom": "19.1.1", "eslint": "^9.33.0", "typescript": "5.9.2"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0"}}