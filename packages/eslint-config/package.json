{"name": "@timewithtuuli/eslint-config", "version": "0.0.0", "type": "module", "private": true, "exports": {"./base": "./base.js", "./next-js": "./next.js", "./react-internal": "./react-internal.js"}, "devDependencies": {"@eslint/js": "^9.33.0", "@next/eslint-plugin-next": "^15.4.2", "eslint": "^9.33.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-only-warn": "^1.1.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-turbo": "^2.5.0", "globals": "^16.3.0", "typescript": "^5.9.2", "typescript-eslint": "^8.39.0"}}